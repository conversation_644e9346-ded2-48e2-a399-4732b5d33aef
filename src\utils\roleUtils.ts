import { User, UserRole } from '../types';

/**
 * Utility functions for handling multi-role user system
 * Provides backward compatibility with single-role system
 */

/**
 * Get all roles for a user (handles both single and multi-role users)
 */
export const getUserRoles = (user: User): UserRole[] => {
  if (user.isMultiRole && user.roles && user.roles.length > 0) {
    return user.roles;
  }
  return [user.role];
};

/**
 * Check if user has a specific role
 */
export const hasRole = (user: User, role: UserRole): boolean => {
  const userRoles = getUserRoles(user);
  return userRoles.includes(role);
};

/**
 * Check if user has any of the specified roles
 */
export const hasAnyRole = (user: User, roles: UserRole[]): boolean => {
  const userRoles = getUserRoles(user);
  return roles.some(role => userRoles.includes(role));
};

/**
 * Check if user has all of the specified roles
 */
export const hasAllRoles = (user: User, roles: UserRole[]): boolean => {
  const userRoles = getUserRoles(user);
  return roles.every(role => userRoles.includes(role));
};

/**
 * Get the primary role for a user (for backward compatibility)
 */
export const getPrimaryRole = (user: User): UserRole => {
  return user.role;
};

/**
 * Check if user is multi-role enabled
 */
export const isMultiRoleUser = (user: User): boolean => {
  return user.isMultiRole === true && user.roles && user.roles.length > 1;
};

/**
 * Get available roles for role switching (for multi-role users)
 */
export const getAvailableRoles = (user: User): UserRole[] => {
  if (isMultiRoleUser(user)) {
    return user.roles || [user.role];
  }
  return [user.role];
};

/**
 * Check if user can access admin features
 */
export const canAccessAdmin = (user: User): boolean => {
  return hasRole(user, 'admin');
};

/**
 * Check if user can access management features
 */
export const canAccessManagement = (user: User): boolean => {
  return hasAnyRole(user, ['admin', 'gebietsmanager', 'teamleiter']);
};

/**
 * Check if user can access mentor features
 */
export const canAccessMentor = (user: User): boolean => {
  return hasAnyRole(user, ['admin', 'mentor']);
};

/**
 * Check if user can access berater features
 */
export const canAccessBerater = (user: User): boolean => {
  return hasAnyRole(user, ['admin', 'berater', 'mentor', 'teamleiter', 'gebietsmanager']);
};

/**
 * Get role hierarchy level (higher number = more permissions)
 */
export const getRoleLevel = (role: UserRole): number => {
  const roleLevels: Record<UserRole, number> = {
    'berater': 1,
    'mentor': 2,
    'teamleiter': 3,
    'gebietsmanager': 4,
    'admin': 5
  };
  return roleLevels[role] || 0;
};

/**
 * Get the highest role level for a user
 */
export const getHighestRoleLevel = (user: User): number => {
  const userRoles = getUserRoles(user);
  return Math.max(...userRoles.map(role => getRoleLevel(role)));
};

/**
 * Format roles for display
 */
export const formatRolesForDisplay = (user: User): string => {
  const roles = getUserRoles(user);
  if (roles.length === 1) {
    return formatSingleRole(roles[0]);
  }
  
  return roles.map(role => formatSingleRole(role)).join(' + ');
};

/**
 * Format a single role for display
 */
export const formatSingleRole = (role: UserRole): string => {
  const roleDisplayNames: Record<UserRole, string> = {
    'berater': 'Berater',
    'mentor': 'Mentor',
    'teamleiter': 'Teamleiter',
    'gebietsmanager': 'Gebietsmanager',
    'admin': 'Administrator'
  };
  return roleDisplayNames[role] || role;
};

/**
 * Check if a role can be assigned to a user (business logic)
 */
export const canAssignRole = (currentUser: User, targetRole: UserRole): boolean => {
  // Only admins can assign admin role
  if (targetRole === 'admin') {
    return hasRole(currentUser, 'admin');
  }
  
  // Admins can assign any role
  if (hasRole(currentUser, 'admin')) {
    return true;
  }
  
  // Other role assignment logic can be added here
  return false;
};

/**
 * Validate role combination for multi-role users
 */
export const isValidRoleCombination = (roles: UserRole[]): boolean => {
  // Admin can be combined with any role
  if (roles.includes('admin')) {
    return true;
  }
  
  // Add other business logic for valid role combinations
  // For now, allow any combination except admin (which is handled above)
  return roles.length > 0;
};
