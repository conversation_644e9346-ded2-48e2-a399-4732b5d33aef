
import React, { Suspense, lazy, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { AuthProvider } from '@/context/auth/AuthContext';
import { DataProvider } from '@/context/data';
import { ThemeProvider } from '@/context/theme/ThemeProvider';
import { SettingsProvider } from '@/context/settings/SettingsProvider';
import { Toaster } from '@/components/ui/sonner';
import ProtectedRoute from '@/components/ProtectedRoute';
import ErrorBoundary from '@/components/ErrorBoundary';
import { setupGlobalMobileOptimizations } from '@/utils/mobileOptimization';

// Lazy load pages for better performance
const Index = lazy(() => import('@/pages/Index'));
const Login = lazy(() => import('@/pages/Login'));
const EFHPage = lazy(() => import('@/pages/EFHPage'));
const MFHPage = lazy(() => import('@/pages/MFHPage'));
const MapPage = lazy(() => import('@/pages/MapPage'));
const DailyViewPage = lazy(() => import('@/pages/DailyViewPage'));
const StatisticsPage = lazy(() => import('@/pages/StatisticsPage'));
const TeamsStatisticsPage = lazy(() => import('@/pages/TeamsStatisticsPage'));
const BeraterStatisticsPage = lazy(() => import('@/pages/BeraterStatisticsPage'));
const ProfilePage = lazy(() => import('@/pages/ProfilePage'));
const SettingsPage = lazy(() => import('@/pages/SettingsPage'));
const CalendarPage = lazy(() => import('@/pages/CalendarPage'));
const UserManagementPage = lazy(() => import('@/pages/UserManagementPage'));
const TeamManagementPage = lazy(() => import('@/pages/TeamManagementPage'));
const AreaManagementPage = lazy(() => import('@/pages/AreaManagementPage'));
const AdminDashboardPage = lazy(() => import('@/pages/AdminDashboardPage'));
const TeamOverviewPage = lazy(() => import('@/pages/TeamOverviewPage'));
const TeamsOverviewPage = lazy(() => import('@/pages/TeamsOverviewPage'));
const AreaOverviewPage = lazy(() => import('@/pages/AreaOverviewPage'));
const VisitStatusPage = lazy(() => import('@/pages/VisitStatusPage'));
const ProductSelectionPage = lazy(() => import('@/pages/ProductSelectionPage'));
const PatternAnalysisDemoPage = lazy(() => import('@/pages/PatternAnalysisDemoPage'));
const NotFound = lazy(() => import('@/pages/NotFound'));
const MFHManagerPage = lazy(() => import('@/pages/MFHManagerPage'));
const ButtonTest = lazy(() => import('@/components/debug/ButtonTest'));
const TouchTargetValidatorPage = lazy(() => import('@/pages/TouchTargetValidatorPage'));

// Loading component for Suspense
const PageLoader = () => (
  <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
    <div className="text-center">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
      <p className="text-neutral-600 font-medium">Lädt...</p>
    </div>
  </div>
);

import './App.css';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      refetchOnWindowFocus: false,
    },
  },
});

function App() {
  // Setup global mobile optimizations
  useEffect(() => {
    setupGlobalMobileOptimizations();
  }, []);

  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <ThemeProvider>
          <SettingsProvider>
            <AuthProvider>
              <DataProvider>
                <Router>
                  <ErrorBoundary>
                    <div className="min-h-screen bg-background">
                      <Suspense fallback={<PageLoader />}>
                        <Routes>
                    <Route path="/login" element={<Login />} />
                    <Route 
                      path="/" 
                      element={
                        <ProtectedRoute>
                          <Index />
                        </ProtectedRoute>
                      } 
                    />
                    <Route 
                      path="/efh" 
                      element={
                        <ProtectedRoute>
                          <EFHPage />
                        </ProtectedRoute>
                      } 
                    />
                    <Route
                      path="/mfh"
                      element={
                        <ProtectedRoute>
                          <MFHPage />
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/mfh/:id"
                      element={
                        <ProtectedRoute>
                          <MFHManagerPage />
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/mfh-manager"
                      element={
                        <ProtectedRoute>
                          <MFHManagerPage />
                        </ProtectedRoute>
                      }
                    />
                    <Route 
                      path="/map" 
                      element={
                        <ProtectedRoute>
                          <MapPage />
                        </ProtectedRoute>
                      } 
                    />
                    <Route 
                      path="/daily-view" 
                      element={
                        <ProtectedRoute>
                          <DailyViewPage />
                        </ProtectedRoute>
                      } 
                    />
                    <Route 
                      path="/statistics" 
                      element={
                        <ProtectedRoute>
                          <StatisticsPage />
                        </ProtectedRoute>
                      } 
                    />
                    <Route 
                      path="/teams-statistics" 
                      element={
                        <ProtectedRoute>
                          <TeamsStatisticsPage />
                        </ProtectedRoute>
                      } 
                    />
                    <Route 
                      path="/berater-statistics" 
                      element={
                        <ProtectedRoute>
                          <BeraterStatisticsPage />
                        </ProtectedRoute>
                      } 
                    />
                    <Route 
                      path="/profile" 
                      element={
                        <ProtectedRoute>
                          <ProfilePage />
                        </ProtectedRoute>
                      } 
                    />
                    <Route 
                      path="/settings" 
                      element={
                        <ProtectedRoute>
                          <SettingsPage />
                        </ProtectedRoute>
                      } 
                    />
                    <Route 
                      path="/calendar" 
                      element={
                        <ProtectedRoute>
                          <CalendarPage />
                        </ProtectedRoute>
                      } 
                    />
                    <Route
                      path="/admin-dashboard"
                      element={
                        <ProtectedRoute allowedRoles={['admin']}>
                          <AdminDashboardPage />
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/user-management"
                      element={
                        <ProtectedRoute>
                          <UserManagementPage />
                        </ProtectedRoute>
                      }
                    />
                    <Route 
                      path="/team-management" 
                      element={
                        <ProtectedRoute>
                          <TeamManagementPage />
                        </ProtectedRoute>
                      } 
                    />
                    <Route 
                      path="/area-management" 
                      element={
                        <ProtectedRoute>
                          <AreaManagementPage />
                        </ProtectedRoute>
                      } 
                    />
                    <Route 
                      path="/team-overview" 
                      element={
                        <ProtectedRoute>
                          <TeamOverviewPage />
                        </ProtectedRoute>
                      } 
                    />
                    <Route 
                      path="/teams-overview" 
                      element={
                        <ProtectedRoute>
                          <TeamsOverviewPage />
                        </ProtectedRoute>
                      } 
                    />
                    <Route 
                      path="/area-overview" 
                      element={
                        <ProtectedRoute>
                          <AreaOverviewPage />
                        </ProtectedRoute>
                      } 
                    />
                    <Route 
                      path="/visit-status" 
                      element={
                        <ProtectedRoute>
                          <VisitStatusPage />
                        </ProtectedRoute>
                      } 
                    />
                    <Route
                      path="/products/:visitId"
                      element={
                        <ProtectedRoute>
                          <ProductSelectionPage />
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/product-selection"
                      element={
                        <ProtectedRoute>
                          <ProductSelectionPage />
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/pattern-analysis-demo"
                      element={
                        <ProtectedRoute>
                          <PatternAnalysisDemoPage />
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/button-test"
                      element={
                        <ProtectedRoute>
                          <ButtonTest />
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/touch-target-validator"
                      element={
                        <ProtectedRoute>
                          <TouchTargetValidatorPage />
                        </ProtectedRoute>
                      }
                    />
                          <Route path="/404" element={<NotFound />} />
                          <Route path="*" element={<Navigate to="/404" replace />} />
                        </Routes>
                      </Suspense>
                    </div>
                    <Toaster />
                  </ErrorBoundary>
                </Router>
              </DataProvider>
            </AuthProvider>
          </SettingsProvider>
        </ThemeProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
}

export default App;
