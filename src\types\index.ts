
// User types
export type UserRole = "berater" | "mentor" | "teamleiter" | "gebietsmanager" | "admin";

export interface User {
  id: string;
  name: string;
  email: string;
  role: UserRole; // Primary role for backward compatibility
  roles?: UserRole[]; // Multiple roles for multi-role users
  teamId?: string;
  mentorId?: string;
  assignedBeraterIds?: string[];
  password?: string; // Added password field for authentication
  isMultiRole?: boolean; // Flag to indicate multi-role user
}

// House and Visit types
export type HouseType = "EFH" | "MFH";
export type VisitStatus = "N/A" | "Angetroffen → Termin" | "Angetroffen → Kein Interesse" | "Angetroffen → Sale";
export type ProductCategory = "KIP" | "TV" | "Mobile";

export interface Address {
  id: string;
  zipCode: string;
  city: string;
  street: string;
}

export interface House {
  id: string;
  addressId: string;
  houseNumber: string;
  type: HouseType;
  latitude: number;
  longitude: number;
  createdAt: string;
  createdBy: string;
}

export interface Visit {
  id: string;
  houseId: string;
  timestamp: string;
  userId: string;
  status: VisitStatus;
  notes?: string;
  comment?: string;
  appointmentDate?: string;
  appointmentTime?: string;
  // Enhanced timestamp data for pattern analysis
  dayOfWeek?: number; // 0-6 (Sunday-Saturday)
  hourOfDay?: number; // 0-23
  isWeekend?: boolean;
  seasonalContext?: 'spring' | 'summer' | 'autumn' | 'winter';
}

export interface Door {
  id: string;
  visitId: string;
  name: string;
  floor?: string;
  status: VisitStatus;
  notes?: string;
  comment?: string;
  appointmentDate?: string;
  appointmentTime?: string;
}

export interface ProductEntry {
  id: string;
  doorId: string;
  category: ProductCategory;
  type: string;
  quantity: number;
  userId: string;
  timestamp: string;
  notes?: string;
}

// New interfaces for intelligent visit optimization
export interface VisitPattern {
  id: string;
  addressId: string;
  patternType: 'time_of_day' | 'day_of_week' | 'seasonal' | 'shift_work';
  patternData: {
    successfulTimes?: number[]; // Hours of day (0-23)
    failedTimes?: number[]; // Hours of day (0-23)
    successfulDays?: number[]; // Days of week (0-6)
    failedDays?: number[]; // Days of week (0-6)
    confidence: number; // 0-1 confidence score
    dataPoints: number; // Number of visits used for analysis
  };
  lastUpdated: string;
  createdAt: string;
}

export interface VisitRecommendation {
  id: string;
  addressId: string;
  recommendationType: 'optimal_time' | 'avoid_time' | 'day_preference' | 'general_advice';
  recommendation: string;
  confidence: number; // 0-1 confidence score
  priority: 'high' | 'medium' | 'low';
  basedOnVisits: number; // Number of failed visits this is based on
  suggestedTimeSlots?: {
    day: number; // 0-6 (Sunday-Saturday)
    startHour: number; // 0-23
    endHour: number; // 0-23
    confidence: number;
  }[];
  createdAt: string;
  lastUpdated: string;
}

export interface AddressVisitHistory {
  addressId: string;
  totalVisits: number;
  failedVisits: number;
  successfulVisits: number;
  lastVisitDate: string;
  patterns: VisitPattern[];
  recommendations: VisitRecommendation[];
}

// Define product options
export const productOptions = [
  { category: 'KIP' as ProductCategory, type: 'KIP Standard', label: 'KIP Standard' },
  { category: 'TV' as ProductCategory, type: 'TV Connect Standard', label: 'TV Connect Standard' },
  { category: 'TV' as ProductCategory, type: 'TV Connect', label: 'TV Connect' },
  { category: 'TV' as ProductCategory, type: 'TV Connect Start', label: 'TV Connect Start' },
  { category: 'TV' as ProductCategory, type: 'Giga TV / Cable Net', label: 'Giga TV / Cable Net' },
  { category: 'TV' as ProductCategory, type: 'Giga TV / Cable Net inkl. Premium', label: 'Giga TV / Cable Net inkl. Premium' },
  { category: 'TV' as ProductCategory, type: 'Giga TV / Cable Net inkl. Netflix', label: 'Giga TV / Cable Net inkl. Netflix' },
  { category: 'TV' as ProductCategory, type: 'Giga TV Mobile-App', label: 'Giga TV Mobile-App' },
  { category: 'TV' as ProductCategory, type: 'Multi Room', label: 'Multi Room' },
  { category: 'TV' as ProductCategory, type: 'Multi Room / Home Sound', label: 'Multi Room / Home Sound' },
  { category: 'Mobile' as ProductCategory, type: 'Giga Mobil XS', label: 'Giga Mobil XS' },
  { category: 'Mobile' as ProductCategory, type: 'Giga Mobil S', label: 'Giga Mobil S' },
  { category: 'Mobile' as ProductCategory, type: 'Giga Mobil M', label: 'Giga Mobil M' },
  { category: 'Mobile' as ProductCategory, type: 'Giga Mobil L', label: 'Giga Mobil L' },
  { category: 'Mobile' as ProductCategory, type: 'Giga Mobil XL', label: 'Giga Mobil XL' },
  { category: 'Mobile' as ProductCategory, type: 'FamilyCard S', label: 'FamilyCard S' },
  { category: 'Mobile' as ProductCategory, type: 'FamilyCard M', label: 'FamilyCard M' },
  { category: 'Mobile' as ProductCategory, type: 'FamilyCard L', label: 'FamilyCard L' },
  { category: 'Mobile' as ProductCategory, type: 'Red + Data', label: 'Red + Data' },
];
